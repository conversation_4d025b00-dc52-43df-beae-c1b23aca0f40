import { InputManager } from '../input/InputManager.js';
import { PlayerShip } from '../entities/PlayerShip.js';
import { GameObjectManager } from '../utils/GameObjectManager.js';
import { LevelManager } from '../managers/LevelManager.js';
import { EnemyManager } from '../managers/EnemyManager.js';
import { TokenEconomyManager } from '../managers/TokenEconomyManager.js';
import { GenieInterface } from '../ui/GenieInterface.js';
import { OrangeSDKManager } from '../managers/OrangeSDKManager.js';

/**
 * Core Game Engine - Main game loop and state management
 * Implements fixed timestep game loop with 60 FPS target
 */
export class GameEngine {
    constructor(canvas, uiElement) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.uiElement = uiElement;
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;
        
        // Fixed timestep configuration
        this.targetFPS = 60;
        this.fixedTimeStep = 1000 / this.targetFPS; // 16.67ms per frame
        this.maxFrameTime = 250; // Maximum frame time to prevent spiral of death
        
        // Timing variables
        this.lastFrameTime = 0;
        this.accumulator = 0;
        this.currentTime = 0;
        
        // Performance tracking
        this.frameCount = 0;
        this.fpsTimer = 0;
        this.currentFPS = 0;
        
        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
    }
    
    async init() {
        console.log('Initializing WarpSpace Game Engine...');
        
        // Set up canvas properties
        this.setupCanvas();
        
        // Initialize game systems (placeholder for now)
        await this.initializeSystems();
        
        // Start the game loop
        this.start();
        
        return Promise.resolve();
    }
    
    setupCanvas() {
        // Set up canvas for crisp pixel rendering
        this.ctx.imageSmoothingEnabled = false;
        
        // Set canvas size
        this.canvas.width = 800;
        this.canvas.height = 600;
        
        console.log(`Canvas initialized: ${this.canvas.width}x${this.canvas.height}`);
    }
    
    async initializeSystems() {
        // Initialize input manager
        this.inputManager = new InputManager(this.canvas);
        
        // Initialize game object manager
        this.gameObjectManager = new GameObjectManager();
        
        // Initialize player ship at bottom center of screen
        const startX = this.canvas.width / 2;
        const startY = this.canvas.height - 100; // 100 pixels from bottom
        this.playerShip = new PlayerShip(startX, startY, this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Initialize level manager
        this.levelManager = new LevelManager(this.gameObjectManager);
        
        // Initialize enemy manager
        this.enemyManager = new EnemyManager(this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Initialize token economy manager
        this.tokenManager = new TokenEconomyManager();

        // Initialize Orange SDK manager
        this.orangeSDKManager = new OrangeSDKManager();

        // Initialize Genie interface
        this.genieInterface = new GenieInterface(this.tokenManager, this);

        // Set up level manager callbacks
        this.setupLevelManagerCallbacks();

        // Set up enemy manager callbacks
        this.setupEnemyManagerCallbacks();

        // Set up Orange SDK manager callbacks
        this.setupOrangeSDKCallbacks();

        // Set up token manager callbacks
        this.setupTokenManagerCallbacks();

        // Initialize Genie interface
        await this.genieInterface.initialize();
        this.setupGenieInterfaceCallbacks();

        // Initialize Orange SDK (async)
        this.initializeOrangeSDK();

        // Start the first level
        this.levelManager.startLevel(1);
        
        console.log('Game systems initialized');
    }
    
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.isPaused = false;
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;
            this.frameCount = 0;
            this.fpsTimer = 0;
            requestAnimationFrame(this.gameLoop);
            console.log('Game loop started with fixed timestep');
        }
    }
    
    pause() {
        this.isPaused = true;
        console.log('Game paused');

        // Notify Orange SDK of pause
        if (this.orangeSDKManager) {
            this.orangeSDKManager.handleGamePause();
        }
    }

    resume() {
        if (this.isPaused) {
            this.isPaused = false;
            // Reset timing to prevent large delta time jump
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;
            console.log('Game resumed');

            // Notify Orange SDK of resume
            if (this.orangeSDKManager) {
                this.orangeSDKManager.handleGameResume();
            }
        }
    }
    
    async destroy() {
        this.isRunning = false;

        // Handle Orange SDK quit before cleanup
        if (this.orangeSDKManager) {
            await this.orangeSDKManager.handleGameQuit();
        }

        // Cleanup input manager
        if (this.inputManager) {
            this.inputManager.destroy();
        }

        console.log('Game engine destroyed');
    }
    
    gameLoop(currentTime) {
        if (!this.isRunning) return;
        
        // Calculate frame time and clamp to prevent spiral of death
        let frameTime = currentTime - this.lastFrameTime;
        if (frameTime > this.maxFrameTime) {
            frameTime = this.maxFrameTime;
        }
        
        this.lastFrameTime = currentTime;
        
        if (!this.isPaused) {
            // Add frame time to accumulator
            this.accumulator += frameTime;
            
            // Fixed timestep updates - run as many updates as needed
            while (this.accumulator >= this.fixedTimeStep) {
                this.update(this.fixedTimeStep);
                this.accumulator -= this.fixedTimeStep;
            }
            
            // Calculate interpolation factor for smooth rendering
            const interpolation = this.accumulator / this.fixedTimeStep;
            
            // Always render (variable timestep for smooth visuals)
            this.render(interpolation);
            
            // Update FPS counter
            this.updateFPSCounter(frameTime);
        }
        
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        // Update input manager
        if (this.inputManager) {
            this.inputManager.update();
        }
        
        // Update player ship with movement input (handle separately from GameObjectManager)
        if (this.playerShip && this.inputManager) {
            const movementInput = this.inputManager.getMovementVector();
            this.playerShip.update(deltaTime, movementInput);
            
            // Handle weapon firing input
            if (this.inputManager.isActionDown('fire')) {
                this.playerShip.fire();
            }
            
            // Debug: Test damage system (D key)
            if (this.inputManager.isKeyPressed('KeyD')) {
                this.playerShip.takeDamage(25); // Take 25 damage for testing
            }
            
            // Debug: Test healing system (H key)
            if (this.inputManager.isKeyPressed('KeyH')) {
                this.playerShip.heal(25); // Heal 25 health for testing
            }
            
            // Debug: Add extra life (L key)
            if (this.inputManager.isKeyPressed('KeyL')) {
                this.playerShip.addLives(1); // Add 1 life for testing
            }

            // Debug: Add tokens (T key)
            if (this.inputManager.isKeyPressed('KeyT')) {
                this.tokenManager.awardTokens(500, 'debug_tokens');
            }

            // Debug: Show Genie interface (G key)
            if (this.inputManager.isKeyPressed('KeyG')) {
                this.showGenieInterface({ levelNumber: 1, nextLevel: 2 });
            }
        }
        
        // Update active power-ups
        if (this.genieInterface) {
            this.genieInterface.updateActivePowerUps(deltaTime);
        }

        // Update level manager
        if (this.levelManager) {
            const gameState = {
                playerDestroyed: this.playerShip ? this.playerShip.getHealthStatus().isDestroyed : false,
                playerDamageTaken: false, // This would be set by damage events
                shotsFired: 0, // This would be tracked by weapon system
                shotsHit: 0 // This would be tracked by collision system
            };
            this.levelManager.update(deltaTime, gameState);
        }
        
        // Update enemy manager
        if (this.enemyManager && this.playerShip) {
            const playerPosition = this.playerShip.position;
            this.enemyManager.update(deltaTime, playerPosition);
            
            // Handle collisions
            this.handleCollisions();
        }
        
        // Update all other game objects (projectiles, enemies, etc.)
        if (this.gameObjectManager) {
            this.gameObjectManager.update(deltaTime);
        }
        
        // Clean up out-of-bounds projectiles
        this.cleanupProjectiles();

        // Update Orange SDK auto-save
        if (this.orangeSDKManager) {
            this.orangeSDKManager.updateAutoSave();
        }
    }
    
    /**
     * Clean up projectiles that are out of bounds
     */
    cleanupProjectiles() {
        if (!this.gameObjectManager) return;
        
        const bounds = {
            left: -50,
            right: this.canvas.width + 50,
            top: -50,
            bottom: this.canvas.height + 50
        };
        
        const projectiles = this.gameObjectManager.findByTag('projectile');
        for (const projectile of projectiles) {
            if (projectile.isOutOfBounds(bounds)) {
                this.gameObjectManager.returnToPool('projectile', projectile);
            }
        }
    }
    
    render(interpolation = 0) {
        // Clear canvas
        this.ctx.fillStyle = '#000011';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render basic space background
        this.renderStarField();
        
        // Render player ship separately
        if (this.playerShip) {
            this.playerShip.render(this.ctx, interpolation);
        }
        
        // Render enemy manager (enemies and their projectiles)
        if (this.enemyManager) {
            this.enemyManager.render(this.ctx, interpolation);
        }
        
        // Render all other game objects (projectiles, enemies, etc.)
        if (this.gameObjectManager) {
            this.gameObjectManager.render(this.ctx, interpolation);
        }
        
        // Game title (smaller and positioned at top)
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '20px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('WarpSpace', this.canvas.width / 2, 30);
        
        // Display input debug info
        if (this.inputManager) {
            this.renderInputDebug();
            
            // Render input manager (virtual joystick, etc.)
            this.inputManager.render(this.ctx);
        }
        
        // Display FPS counter
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`FPS: ${this.currentFPS}`, 10, 20);
        
        // Display health and lives UI
        this.renderHealthAndLivesUI();
        
        // Display level and score UI
        this.renderLevelAndScoreUI();
        
        // Display token balance and reward animations
        if (this.tokenManager) {
            this.tokenManager.render(this.ctx, this.fixedTimeStep);
        }
    }
    
    renderInputDebug() {
        if (!this.inputManager) return;
        
        // Display movement input
        const movement = this.inputManager.getMovementVector();
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText(`Movement: ${movement.x.toFixed(2)}, ${movement.y.toFixed(2)}`, 10, 40);
        
        // Display active actions
        const actions = ['fire', 'pause', 'interact'];
        let yOffset = 60;
        
        for (const action of actions) {
            if (this.inputManager.isActionDown(action)) {
                this.ctx.fillStyle = '#00ff00';
                this.ctx.fillText(`${action.toUpperCase()}: ACTIVE`, 10, yOffset);
            } else {
                this.ctx.fillStyle = '#666666';
                this.ctx.fillText(`${action}: inactive`, 10, yOffset);
            }
            yOffset += 15;
        }
        
        // Display mouse position
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText(`Mouse: ${this.inputManager.mousePosition.x.toFixed(0)}, ${this.inputManager.mousePosition.y.toFixed(0)}`, 10, yOffset + 10);
        
        // Display touch info for mobile
        if (this.inputManager.isTouchDevice) {
            this.ctx.fillText(`Touch Device: ${this.inputManager.touches.size} touches`, 10, yOffset + 25);
        }
        
        // Display game object manager stats
        if (this.gameObjectManager) {
            const stats = this.gameObjectManager.getStats();
            this.ctx.fillText(`Objects: ${stats.totalObjects} (${stats.activeObjects} active)`, 10, yOffset + 40);
            
            const projectiles = this.gameObjectManager.findByTag('projectile');
            this.ctx.fillText(`Projectiles: ${projectiles.length}`, 10, yOffset + 55);
        }
    }
    
    updateFPSCounter(frameTime) {
        this.frameCount++;
        this.fpsTimer += frameTime;
        
        // Update FPS display every second
        if (this.fpsTimer >= 1000) {
            this.currentFPS = Math.round((this.frameCount * 1000) / this.fpsTimer);
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }
    
    renderStarField() {
        // Simple star field effect
        this.ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 100; i++) {
            const x = (i * 37) % this.canvas.width;
            const y = (i * 73) % this.canvas.height;
            const size = (i % 3) + 1;
            this.ctx.fillRect(x, y, size, size);
        }
    }
    
    /**
     * Render health bar and lives counter UI
     */
    renderHealthAndLivesUI() {
        if (!this.playerShip) return;
        
        const healthStatus = this.playerShip.getHealthStatus();
        
        // Health bar position (top-right corner)
        const healthBarX = this.canvas.width - 220;
        const healthBarY = 15;
        const healthBarWidth = 200;
        const healthBarHeight = 20;
        
        // Health bar background
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);
        
        // Health bar border
        this.ctx.strokeStyle = '#666666';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);
        
        // Health bar fill
        const healthWidth = healthBarWidth * healthStatus.healthPercentage;
        let healthColor = '#00ff00'; // Green for healthy
        
        if (healthStatus.healthPercentage < 0.3) {
            healthColor = '#ff0000'; // Red for critical
        } else if (healthStatus.healthPercentage < 0.6) {
            healthColor = '#ffaa00'; // Orange for damaged
        }
        
        this.ctx.fillStyle = healthColor;
        this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);
        
        // Health text
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `${healthStatus.health}/${healthStatus.maxHealth}`, 
            healthBarX + healthBarWidth / 2, 
            healthBarY + healthBarHeight / 2 + 4
        );
        
        // Lives counter (below health bar)
        const livesY = healthBarY + healthBarHeight + 25;
        this.ctx.textAlign = 'right';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.fillText(`Lives: ${healthStatus.lives}`, this.canvas.width - 20, livesY);
        
        // Draw life icons
        const lifeIconSize = 16;
        const lifeIconSpacing = 20;
        const lifeIconStartX = this.canvas.width - 20 - (healthStatus.lives * lifeIconSpacing);
        
        for (let i = 0; i < healthStatus.lives; i++) {
            const iconX = lifeIconStartX + (i * lifeIconSpacing);
            const iconY = livesY + 10;
            
            // Draw small ship icon for each life
            this.ctx.fillStyle = '#4A90E2';
            this.ctx.beginPath();
            this.ctx.moveTo(iconX, iconY - lifeIconSize / 2);
            this.ctx.lineTo(iconX - lifeIconSize / 3, iconY + lifeIconSize / 3);
            this.ctx.lineTo(iconX + lifeIconSize / 3, iconY + lifeIconSize / 3);
            this.ctx.closePath();
            this.ctx.fill();
        }
        
        // Show invulnerability status
        if (healthStatus.isInvulnerable) {
            this.ctx.fillStyle = '#ffff00';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'right';
            const invulnTime = (healthStatus.invulnerabilityTimeRemaining / 1000).toFixed(1);
            this.ctx.fillText(`Invulnerable: ${invulnTime}s`, this.canvas.width - 20, livesY + 50);
        }
        
        // Show game over message if destroyed
        if (healthStatus.isDestroyed) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            
            this.ctx.fillStyle = '#ff0000';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('GAME OVER', this.canvas.width / 2, this.canvas.height / 2);
            
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '24px Arial';
            this.ctx.fillText('No lives remaining', this.canvas.width / 2, this.canvas.height / 2 + 50);
        }
    }
    
    /**
     * Render level and score UI
     */
    renderLevelAndScoreUI() {
        if (!this.levelManager) return;
        
        const levelStatus = this.levelManager.getLevelStatus();
        
        // Level information (top-left corner)
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`Level: ${levelStatus.currentLevel}`, 10, 120);
        
        // Score display
        this.ctx.fillText(`Score: ${levelStatus.score.current.toLocaleString()}`, 10, 140);
        this.ctx.font = '12px Arial';
        this.ctx.fillText(`Level Score: ${levelStatus.score.level.toLocaleString()}`, 10, 155);
        
        // Progress display
        if (levelStatus.levelInProgress) {
            const progressText = `Enemies: ${levelStatus.progress.enemiesDefeated}/${levelStatus.progress.requiredEnemies}`;
            this.ctx.fillText(progressText, 10, 175);
            
            const waveText = `Waves: ${levelStatus.progress.wavesCompleted}/${levelStatus.progress.requiredWaves}`;
            this.ctx.fillText(waveText, 10, 190);
            
            // Level timer
            const timeElapsed = (levelStatus.completionTime / 1000).toFixed(1);
            this.ctx.fillText(`Time: ${timeElapsed}s`, 10, 205);
            
            // Time limit warning
            if (levelStatus.levelConfig && levelStatus.levelConfig.timeLimit) {
                const timeRemaining = levelStatus.levelConfig.timeLimit - (levelStatus.completionTime / 1000);
                if (timeRemaining <= 30 && timeRemaining > 0) {
                    this.ctx.fillStyle = '#ff6600';
                    this.ctx.fillText(`Time Remaining: ${timeRemaining.toFixed(1)}s`, 10, 220);
                } else if (timeRemaining <= 10 && timeRemaining > 0) {
                    this.ctx.fillStyle = '#ff0000';
                    this.ctx.fillText(`TIME CRITICAL: ${timeRemaining.toFixed(1)}s`, 10, 220);
                }
            }
        }
        
        // Performance indicators
        if (levelStatus.performance.perfectCompletion) {
            this.ctx.fillStyle = '#00ff00';
            this.ctx.font = '10px Arial';
            this.ctx.fillText('PERFECT RUN', 10, 240);
        }
        
        // Environment display
        if (levelStatus.levelConfig && levelStatus.levelConfig.environment) {
            this.ctx.fillStyle = '#cccccc';
            this.ctx.font = '12px Arial';
            const envText = `Environment: ${levelStatus.levelConfig.environment.toUpperCase()}`;
            this.ctx.fillText(envText, 10, 260);
        }
        
        // Level completion overlay
        if (!levelStatus.levelInProgress && levelStatus.levelConfig) {
            this.renderLevelCompletionOverlay(levelStatus);
        }
    }
    
    /**
     * Render level completion overlay
     * @param {object} levelStatus - Current level status
     */
    renderLevelCompletionOverlay(levelStatus) {
        // Semi-transparent overlay
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Level complete text
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '36px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`Level ${levelStatus.currentLevel} Complete!`, this.canvas.width / 2, this.canvas.height / 2 - 50);
        
        // Score breakdown
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '18px Arial';
        this.ctx.fillText(`Final Score: ${levelStatus.score.level.toLocaleString()}`, this.canvas.width / 2, this.canvas.height / 2);
        
        this.ctx.font = '14px Arial';
        this.ctx.fillText(`Enemies Defeated: ${levelStatus.progress.enemiesDefeated}`, this.canvas.width / 2, this.canvas.height / 2 + 25);
        
        const timeText = `Completion Time: ${(levelStatus.completionTime / 1000).toFixed(2)}s`;
        this.ctx.fillText(timeText, this.canvas.width / 2, this.canvas.height / 2 + 45);
        
        // Next level indicator
        this.ctx.fillStyle = '#cccccc';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('Preparing next level...', this.canvas.width / 2, this.canvas.height / 2 + 80);
    }
    
    /**
     * Set up level manager callbacks
     */
    setupLevelManagerCallbacks() {
        // Level start callback
        this.levelManager.setOnLevelStart((levelNumber, levelConfig) => {
            console.log(`Level ${levelNumber} started:`, levelConfig);
            // Could trigger UI updates, music changes, etc.
        });
        
        // Level completion callback
        this.levelManager.setOnLevelComplete((completionData) => {
            console.log('Level completed:', completionData);
            
            if (completionData.completed) {
                // Level completed successfully
                console.log(`Level ${completionData.levelNumber} completed in ${completionData.completionTime.toFixed(2)}s`);
                console.log(`Score: ${completionData.score.totalScore}, Enemies: ${completionData.enemiesDefeated}`);
                
                // Calculate and award WISH tokens
                if (this.tokenManager) {
                    const tokenReward = this.tokenManager.calculateLevelReward(completionData);
                    if (tokenReward.totalReward > 0) {
                        const awardResult = this.tokenManager.awardTokens(
                            tokenReward.totalReward, 
                            'level_completion',
                            {
                                levelNumber: completionData.levelNumber,
                                completionTime: completionData.completionTime,
                                score: completionData.score.totalScore,
                                bonuses: completionData.bonuses,
                                breakdown: tokenReward.breakdown
                            }
                        );
                        
                        console.log(`Awarded ${tokenReward.totalReward} WISH tokens for level completion`);
                        console.log('Token reward breakdown:', tokenReward.breakdown);

                        // Update completion data with token reward for Orange SDK
                        completionData.score.tokenReward = tokenReward.totalReward;
                    }
                }

                // Save progress to Orange SDK
                if (this.orangeSDKManager) {
                    this.orangeSDKManager.onLevelCompleted(completionData);
                }

                // Show Genie interface for power-up purchases
                this.showGenieInterface(completionData);
            } else {
                // Level failed
                console.log(`Level ${completionData.levelNumber} failed: ${completionData.reason}`);
                
                // Auto-retry after a brief delay
                setTimeout(() => {
                    if (completionData.canRetry) {
                        this.levelManager.startLevel(completionData.levelNumber);
                    }
                }, 3000);
            }
        });
        
        // Score update callback
        this.levelManager.setOnScoreUpdate((scoreData) => {
            // Update UI with new score information
            // This could trigger score display animations, etc.
        });
    }
    
    /**
     * Set up enemy manager callbacks
     */
    setupEnemyManagerCallbacks() {
        // Wave completion callback
        this.enemyManager.onWaveComplete = (waveNumber, bonus) => {
            console.log(`Wave ${waveNumber} completed with bonus: ${bonus}`);
            
            // Record wave completion in level manager
            if (this.levelManager) {
                this.levelManager.recordWaveCompletion(waveNumber, bonus);
            }
        };
    }

    /**
     * Set up token manager callbacks
     */
    setupTokenManagerCallbacks() {
        // Balance update callback
        this.tokenManager.setOnBalanceUpdate((balance, statistics) => {
            console.log(`Token balance updated: ${balance} WISH tokens`);

            // Notify Orange SDK of token changes
            if (this.orangeSDKManager) {
                this.orangeSDKManager.onTokensChanged({
                    balance: balance,
                    statistics: statistics
                });
            }
        });

        // Transaction callback
        this.tokenManager.setOnTransaction((transaction) => {
            console.log('Token transaction:', transaction);

            // Notify Orange SDK of significant token changes
            if (this.orangeSDKManager && transaction.amount >= 50) {
                const tokenData = transaction.type === 'earned'
                    ? { earned: transaction.amount }
                    : { spent: transaction.amount };

                this.orangeSDKManager.onTokensChanged(tokenData);
            }
        });

        // Reward earned callback
        this.tokenManager.setOnRewardEarned((amount, reason, metadata) => {
            console.log(`Token reward earned: ${amount} for ${reason}`);
        });
    }

    /**
     * Set up Genie interface callbacks
     */
    setupGenieInterfaceCallbacks() {
        // Power-up purchased callback
        this.genieInterface.setOnPowerUpPurchased((powerUp, transaction) => {
            console.log(`Power-up purchased: ${powerUp.type} for ${powerUp.cost} tokens`);

            // Update active power-ups tracking
            // The GenieInterface already handles the power-up application
        });

        // Reality warp purchased callback (for future implementation)
        this.genieInterface.setOnWarpPurchased((warpOption, transaction) => {
            console.log(`Reality warp purchased: ${warpOption.type} for ${warpOption.cost} tokens`);
        });

        // Interface closed callback
        this.genieInterface.setOnClose(() => {
            console.log('Genie interface closed, resuming game');
            this.continueToNextLevel();
        });
    }

    /**
     * Show the Genie interface between levels
     */
    showGenieInterface(completionData) {
        console.log('Showing Genie interface for level completion');

        // Store completion data for later use
        this.lastCompletionData = completionData;

        // Pause the game
        this.pause();

        // Show the Genie interface
        this.genieInterface.show();
    }

    /**
     * Continue to the next level after Genie interface is closed
     */
    continueToNextLevel() {
        if (this.lastCompletionData) {
            const completionData = this.lastCompletionData;
            this.lastCompletionData = null;

            // Continue to next level or end game
            if (completionData.nextLevel <= this.levelManager.maxLevels) {
                console.log(`Starting level ${completionData.nextLevel}`);
                this.levelManager.startLevel(completionData.nextLevel);
            } else {
                console.log('Game completed! All levels finished.');
                // TODO: Show game completion screen
            }
        }
    }

    /**
     * Handle collisions between game objects
     */
    handleCollisions() {
        if (!this.enemyManager || !this.playerShip) return;
        
        // Check player-enemy collisions
        const playerCollisions = this.enemyManager.checkPlayerCollisions(this.playerShip);
        for (const enemy of playerCollisions) {
            const result = this.enemyManager.handlePlayerEnemyCollision(this.playerShip, enemy);
            console.log('Player-Enemy collision:', result);
        }
        
        // Check projectile-enemy collisions
        const projectiles = this.gameObjectManager.findByTag('projectile');
        const projectileCollisions = this.enemyManager.checkProjectileCollisions(projectiles);
        
        for (const collision of projectileCollisions) {
            const result = this.enemyManager.handleProjectileEnemyCollision(
                collision.projectile, 
                collision.enemy, 
                collision.damage
            );
            
            // Record enemy defeat in level manager
            if (result.enemyDestroyed && this.levelManager) {
                this.levelManager.recordEnemyDefeat(collision.enemy, result.scoreGained);
            }
        }
    }
    
    handleResize() {
        // Handle window resize if needed
        console.log('Window resized');
    }

    /**
     * Initialize Orange SDK asynchronously
     */
    async initializeOrangeSDK() {
        try {
            await this.orangeSDKManager.initialize();
            console.log('Orange SDK initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Orange SDK:', error);
        }
    }

    /**
     * Set up Orange SDK manager callbacks
     */
    setupOrangeSDKCallbacks() {
        // Set up special status callback to handle bonuses
        this.orangeSDKManager.setOnSpecialStatusCallback((statusType, value) => {
            this.handleSpecialStatus(statusType, value);
        });

        // Set up save success callback
        this.orangeSDKManager.setOnDataSavedCallback((data, reason) => {
            console.log(`Game data saved to Orange SDK (${reason})`);
        });

        // Set up save error callback
        this.orangeSDKManager.setOnSaveErrorCallback((error, reason) => {
            console.error(`Failed to save game data (${reason}):`, error);
        });
    }

    /**
     * Handle special status bonuses from Orange SDK
     * @param {string} statusType - Type of special status
     * @param {*} value - Status value
     */
    handleSpecialStatus(statusType, value) {
        switch (statusType) {
            case 'bonus_lives':
                if (this.playerShip && value > 0) {
                    this.playerShip.addLives(value);
                    console.log(`Bonus lives awarded: ${value} (login streak bonus)`);
                }
                break;

            case 'tournament_participant':
                if (value) {
                    console.log('Tournament participant status active');
                    // Could add special tournament UI indicators or bonuses
                }
                break;

            case 'achievement_unlock':
                console.log(`Achievement unlocked: ${value}`);
                // Could trigger achievement notification UI
                break;

            default:
                console.log(`Unknown special status: ${statusType} = ${value}`);
        }
    }
}